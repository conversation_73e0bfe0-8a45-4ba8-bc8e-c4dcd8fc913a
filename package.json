{"name": "uni-app-xiaotuxian", "version": "0.0.0", "author": {"name": "<PERSON>heima", "url": "https://web.itheima.com/"}, "scripts": {"dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "tsc": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "prepare": "husky install", "lint-staged": "lint-staged"}, "lint-staged": {"*.{js,ts,vue}": ["eslint --fix"]}, "dependencies": {"@dcloudio/uni-app": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-app-plus": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-components": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-h5": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-mp-alipay": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-mp-baidu": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-mp-jd": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-mp-kuaishou": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-mp-lark": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-mp-qq": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-mp-toutiao": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-mp-weixin": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-mp-xhs": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-quickapp-webview": "3.0.0-alpha-3081220230802001", "pinia": "2.0.27", "pinia-plugin-persistedstate": "^3.2.0", "vue": "^3.2.47", "vue-i18n": "^9.2.2"}, "devDependencies": {"@dcloudio/types": "^3.3.3", "@dcloudio/uni-automator": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-cli-shared": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-stacktracey": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-vue-devtools": "3.0.0-alpha-3080220230511001", "@dcloudio/vite-plugin-uni": "3.0.0-alpha-3081220230802001", "@rushstack/eslint-patch": "^1.1.4", "@types/node": "^18.11.9", "@uni-helper/uni-app-types": "^0.5.8", "@uni-helper/uni-ui-types": "^0.5.11", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^11.0.0", "@vue/runtime-core": "^3.2.45", "@vue/tsconfig": "^0.4.0", "eslint": "^8.22.0", "eslint-plugin-vue": "^9.3.0", "husky": "^8.0.0", "lint-staged": "^13.0.3", "miniprogram-api-typings": "^3.12.0", "prettier": "^2.7.1", "sass": "^1.56.1", "typescript": "^5.1.6", "vite": "^4.4.9", "vue-tsc": "^1.8.8"}}